<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Klicktape - Reset Password</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Exo+2:wght@400;700&family=Space+Mono:wght@400;700&display=swap"
      rel="stylesheet"
    />
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <style>
      body {
        margin: 0;
        background: linear-gradient(to bottom, #000000, #1a1a33);
        color: #fff8e1;
        font-family: "Space Mono", monospace;
        overflow-x: hidden;
      }
      .prism-card {
        background: linear-gradient(
          135deg,
          rgba(26, 26, 51, 0.9),
          rgba(0, 0, 0, 0.7)
        );
        border: 1px solid #ffd700;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .prism-card:hover {
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
      }
      input {
        background: rgba(255, 248, 225, 0.1);
        border: 1px solid #ffd700;
        color: #fff8e1;
        transition: border-color 0.3s ease;
      }
      input:focus {
        border-color: #ff9500;
        outline: none;
        box-shadow: 0 0 10px rgba(255, 149, 0, 0.5);
      }
      label {
        color: #ffd700;
      }
      ::placeholder {
        color: #fff8e1;
        opacity: 0.7;
      }
      .btn-gold {
        background: rgba(255, 215, 0, 0.2);
        border: 1px solid rgba(255, 215, 0, 0.3);
        transition: all 0.3s ease;
      }
      .btn-gold:hover:not(:disabled) {
        background: rgba(255, 215, 0, 0.3);
        box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
      }
      .btn-gold:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body class="min-h-screen flex items-center justify-center p-4">
    <div class="prism-card w-full max-w-md p-8 rounded-xl">
      <h1 class="text-4xl font-exo text-gold text-center mb-8">
        Reset Password
      </h1>

      <div class="mb-6">
        <label for="password" class="block font-space mb-2">New Password</label>
        <input
          type="password"
          id="password"
          placeholder="Enter your new password"
          class="w-full p-3 rounded-lg text-base font-space"
        />
      </div>

      <div class="mb-8">
        <label for="confirmPassword" class="block font-space mb-2"
          >Confirm Password</label
        >
        <input
          type="password"
          id="confirmPassword"
          placeholder="Confirm your new password"
          class="w-full p-3 rounded-lg text-base font-space"
        />
      </div>

      <button
        id="resetBtn"
        class="btn-gold w-full py-4 px-6 rounded-lg text-white font-space text-lg shadow-md"
      >
        Reset Password
      </button>

      <div id="loading" class="hidden text-center mt-4">
        <div
          class="inline-block w-6 h-6 border-2 border-gold border-t-transparent rounded-full animate-spin"
        ></div>
      </div>
    </div>

    <script>
      // Initialize Supabase client
      const supabase = window.supabase.createClient(
        SUPABASE_CONFIG.url,
        SUPABASE_CONFIG.anonKey
      );

      // DOM elements
      const passwordInput = document.getElementById("password");
      const confirmPasswordInput = document.getElementById("confirmPassword");
      const resetBtn = document.getElementById("resetBtn");
      const loadingEl = document.getElementById("loading");

      // Parse URL hash for tokens
      function parseHash() {
        if (!window.location.hash) return null;

        const hash = window.location.hash.substring(1);
        const params = new URLSearchParams(hash);

        return {
          access_token: params.get("access_token"),
          refresh_token: params.get("refresh_token"),
          type: params.get("type"),
        };
      }

      // Set loading state
      function setLoading(isLoading) {
        resetBtn.disabled = isLoading;
        loadingEl.classList.toggle("hidden", !isLoading);
        resetBtn.textContent = isLoading ? "" : "Reset Password";
        if (isLoading) {
          resetBtn.appendChild(loadingEl);
          loadingEl.classList.remove("hidden");
        }
      }

      // Show alert
      function showAlert(title, message, callback) {
        alert(`${title}\n\n${message}`);
        if (callback) callback();
      }

      // Set session with access token
      async function setSession(tokens) {
        setLoading(true);
        try {
          const { data, error } = await supabase.auth.setSession({
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token || "",
          });

          if (error) throw error;
          if (!data.session) {
            throw new Error("Session not established");
          }

          // Clear the hash from URL for security
          window.history.replaceState(null, null, window.location.pathname);

          return true;
        } catch (error) {
          console.error("Session error:", error);
          showAlert("Error", "Please try again later.", () => {
            // Stay on the same page instead of redirecting
            return;
          });
          return false;
        } finally {
          setLoading(false);
        }
      }

      // Handle form submission
      async function handleSubmit() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (!password || !confirmPassword) {
          showAlert("Error", "Please fill in all fields");
          return;
        }

        if (password.length < 6) {
          showAlert("Error", "Password must be at least 6 characters long");
          return;
        }

        if (password !== confirmPassword) {
          showAlert("Error", "Passwords do not match");
          return;
        }

        setLoading(true);
        try {
          const { error } = await supabase.auth.updateUser({
            password,
          });

          if (error) throw error;

          showAlert(
            "Success",
            "Password reset successfully! Please return to the app and sign in with your new password.",
            () => {}
          );
        } catch (error) {
          console.error("Reset password error:", error);
          showAlert(
            "Error",
            error.message || "Failed to reset password. Please try again."
          );
        } finally {
          setLoading(false);
        }
      }

      // Initialize the page
      document.addEventListener("DOMContentLoaded", async () => {
        // Check for tokens in URL
        const tokens = parseHash();

        if (tokens && tokens.access_token) {
          // If we have tokens and it's a recovery type, set up the session
          if (tokens.type === "recovery") {
            const success = await setSession(tokens);
            if (!success) {
              // If session setup failed, redirect to sign-in
              window.location.href = "/";
              return;
            }
          }
        } else {
          // No tokens in URL, check if user is already authenticated
          const {
            data: { session },
          } = await supabase.auth.getSession();
          if (!session) {
            // Not authenticated and no tokens, redirect to sign-in
            showAlert(
              "Password Reset",
              "To reset your password, please use the 'Forgot Password' option on the sign in screen.",
              () => (window.location.href = "/")
            );
            return;
          }
        }

        // Set up event listener for the reset button
        resetBtn.addEventListener("click", handleSubmit);
      });
    </script>
  </body>
</html>