<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Klicktape - Privacy-First Social Media</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@400;700&family=Space+Mono:wght@400;700&display=swap" rel="stylesheet" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              gold: '#FFD700',
              black: '#000000',
              crystal: '#1A1A33',
              prism: '#FF9500',
              lightgold: '#FFF8E1',
            },
            boxShadow: {
              prism: '0 0 30px rgba(255, 149, 0, 0.8), 0 0 60px rgba(255, 149, 0, 0.4)',
              gold: '0 0 25px rgba(255, 215, 0, 0.9)',
            },
            fontFamily: {
              exo: ['Exo 2', 'sans-serif'],
              space: ['Space Mono', 'monospace'],
            },
          },
        },
      };
    </script>
    <style>
      body {
        margin: 0;
        background: #000000;
        color: #FFF8E1;
        font-family: 'Space Mono', monospace;
        overflow-x: hidden;
        cursor: none;
      }
      #lattice {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        opacity: 0.5; /* Reduced for mobile performance */
      }
      .prism-card {
        background: linear-gradient(135deg, rgba(26, 26, 51, 0.9), rgba(0, 0, 0, 0.7));
        border: 1px solid #FFD700;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .prism-card:hover {
        transform: scale(1.02); /* Simplified for mobile */
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
      }
      .cursor {
        display: none; /* Hidden on mobile by default */
      }
      @media (min-width: 768px) {
        .cursor {
          display: block;
          position: fixed;
          width: 25px;
          height: 25px;
          border: 2px solid #FFD700;
          border-radius: 50%;
          pointer-events: none;
          z-index: 9999;
          transition: transform 0.2s ease, border-color 0.3s ease;
        }
        .cursor.hover {
          transform: scale(1.5);
          border-color: #FF9500;
        }
      }
      .liquid-gold {
        animation: liquid 4s infinite;
      }
      @keyframes liquid {
        0%, 100% { transform: translateY(0); }
        25% { transform: translateY(-3px); }
        75% { transform: translateY(3px); }
      }
      .pulse-prism {
        animation: pulse-prism 3s infinite;
      }
      @keyframes pulse-prism {
        0%, 100% { opacity: 0.9; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.02); }
      }
      .reveal {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.8s ease-out;
      }
      .reveal.active {
        opacity: 1;
        transform: translateY(0);
      }
      #genesis {
        position: fixed;
        inset: 0;
        background: #000000;
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center; /* Ensures text is centered horizontally */
        padding: 1rem; /* Adds some breathing room on small screens */
        animation: fadeOut 4s forwards 2s;
      }
      @keyframes fadeOut {
        to { opacity: 0; pointer-events: none; }
      }
      p, h3 {
        line-height: 1.6;
        letter-spacing: 0.5px;
      }
      section {
        padding: 4rem 1rem; /* Reduced padding for mobile */
      }
      @media (min-width: 768px) {
        section {
          padding: 6rem 0;
        }
      }
      /* Mobile Navbar */
      .mobile-menu {
        display: none; /* Hidden by default */
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        z-index: 100;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transform: translateX(100%); /* Start off-screen to the right */
        transition: transform 0.3s ease-in-out; /* Smooth transition */
      }
      .mobile-menu.active {
        display: flex; /* Show when active */
        transform: translateX(0); /* Slide in */
      }
      .hamburger {
        display: block;
        font-size: 2rem;
        color: #FFD700;
        cursor: pointer;
        z-index: 101; /* Ensure it’s above other elements */
      }
      @media (min-width: 768px) {
        .hamburger {
          display: none;
        }
        .mobile-menu {
          display: none !important; /* Force hide on desktop */
        }
      }
    </style>
  </head>
  <body>
    <!-- 3D Prism Lattice Background -->
    <div id="lattice"></div>

    <!-- Genesis Animation -->
    <div id="genesis">
      <h1 class="text-4xl sm:text-6xl md:text-8xl font-exo text-gold liquid-gold">Privacy-First Social Media</h1>
    </div>

    <!-- Custom Cursor -->
    <div class="cursor"></div>

    <!-- Navbar -->
    <nav class="fixed top-0 w-full bg-black/90 backdrop-blur-2xl z-50 shadow-gold">
      <div class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
        <a href="#" class="text-2xl sm:text-3xl font-exo text-gold liquid-gold">Klicktape</a>
        <div class="hidden md:flex space-x-8 text-sm font-space uppercase tracking-widest">
          <a href="#features" class="hover:text-prism transition-colors">Features</a>
          <a href="#about" class="hover:text-prism transition-colors">About</a>
          <a href="#how-it-works" class="hover:text-prism transition-colors">How It Works</a>
          <a href="#testimonials" class="hover:text-prism transition-colors">Testimonials</a>
          <a href="#faq" class="hover:text-prism transition-colors">FAQ</a>
        </div>
        <a href="form/form.html" class="hidden md:block bg-gradient-to-r from-gold to-prism text-black px-4 py-2 rounded-full font-space hover:shadow-prism transition-all">Join Waitlist</a>
        <div class="hamburger md:hidden">☰</div>
      </div>
      <!-- Mobile Menu -->
      <div class="mobile-menu">
        <div class="absolute top-4 right-4 text-3xl text-gold cursor-pointer close-menu">×</div>
        <a href="#features" class="text-2xl font-space text-lightgold py-4 hover:text-prism">Features</a>
        <a href="#about" class="text-2xl font-space text-lightgold py-4 hover:text-prism">About</a>
        <a href="#how-it-works" class="text-2xl font-space text-lightgold py-4 hover:text-prism">How It Works</a>
        <a href="#testimonials" class="text-2xl font-space text-lightgold py-4 hover:text-prism">Testimonials</a>
        <a href="#faq" class="text-2xl font-space text-lightgold py-4 hover:text-prism">FAQ</a>
        <a href="form/form.html" class="bg-gradient-to-r from-gold to-prism text-black px-6 py-3 rounded-full font-space text-lg mt-6 hover:shadow-prism transition-all">Join Waitlist</a>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
      <div class="max-w-7xl mx-auto px-4 text-center z-10">
        <h1 class="text-3xl sm:text-5xl md:text-7xl font-exo font-bold mb-6 text-gold liquid-gold">Social Media, Reimagined.</h1>
        <p class="text-lg sm:text-xl md:text-2xl text-lightgold font-space mb-8 max-w-3xl mx-auto italic">"A privacy-first platform where you can connect freely, without ads or data exploitation."</p>
        <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <a href="form/form.html" class="bg-gradient-to-r from-gold to-prism text-black px-8 py-3 rounded-full font-space text-lg hover:shadow-gold transition-all">Get Started</a>
          <a href="#features" class="border-2 border-gold text-gold px-8 py-3 rounded-full font-space text-lg hover:bg-gold/10 transition-all">Learn More</a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="relative">
      <div class="max-w-7xl mx-auto px-4">
        <h2 class="text-3xl sm:text-4xl md:text-6xl font-exo text-center mb-12 text-gold pulse-prism">Why Choose Klicktape?</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div class="reveal prism-card p-8 rounded-2xl">
            <h3 class="text-xl sm:text-2xl font-exo mb-4 text-gold liquid-gold">Ad-Free Experience</h3>
            <p class="text-lightgold font-space">Enjoy social media without ads or data tracking. Your privacy is our priority.</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl">
            <h3 class="text-xl sm:text-2xl font-exo mb-4 text-gold liquid-gold">Anonymous Rooms</h3>
            <p class="text-lightgold font-space">Connect with others anonymously and share freely without fear of judgment.</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl">
            <h3 class="text-xl sm:text-2xl font-exo mb-4 text-gold liquid-gold">Gamified Engagement</h3>
            <p class="text-lightgold font-space">Create reels, earn rewards, and climb the leaderboard in a fun, engaging way.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="bg-gradient-to-b from-black to-crystal">
      <div class="max-w-7xl mx-auto px-4">
        <h2 class="text-3xl sm:text-4xl md:text-6xl font-exo text-center mb-12 text-gold">How It Works</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div class="reveal prism-card p-8 rounded-2xl text-center">
            <div class="text-4xl font-bold text-prism mb-4 pulse-prism">1</div>
            <h3 class="text-xl sm:text-2xl font-exo mb-4 text-gold">Sign Up</h3>
            <p class="text-lightgold font-space">Create your account in seconds. No personal data required—just an email and password.</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl text-center">
            <div class="text-4xl font-bold text-prism mb-4 pulse-prism">2</div>
            <h3 class="text-xl sm:text-2xl font-exo mb-4 text-gold">Customize Your Profile</h3>
            <p class="text-lightgold font-space">Set up your profile with a photo and bio. You control what you share—no tracking, no ads.</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl text-center">
            <div class="text-4xl font-bold text-prism mb-4 pulse-prism">3</div>
            <h3 class="text-xl sm:text-2xl font-exo mb-4 text-gold">Connect Freely</h3>
            <p class="text-lightgold font-space">Join anonymous rooms, share reels, and engage with others without worrying about privacy.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="relative">
      <div class="max-w-7xl mx-auto px-4">
        <h2 class="text-3xl sm:text-4xl md:text-6xl font-exo text-center mb-12 text-gold pulse-prism">About Klicktape</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div class="reveal prism-card p-8 rounded-2xl">
            <p class="text-lightgold font-space mb-4">Klicktape is a privacy-first social media platform designed to give you control over your data. We believe in creating a safe, ad-free space where you can connect authentically.</p>
            <p class="text-lightgold font-space mb-6">Our mission is to redefine social media with trust and transparency. Join us in building a better digital future.</p>
            <a href="form/form.html" class="bg-gradient-to-r from-gold to-prism text-black px-6 py-3 rounded-full font-space hover:shadow-gold transition-all">Join the Movement</a>
          </div>
          <div class="reveal">
            <div class="w-full h-64 sm:h-80 md:h-[400px] rounded-2xl overflow-hidden shadow-gold">
              <video autoplay muted loop class="w-full h-full object-cover opacity-70">
                <source src="/Subtitle.mp4" type="video/mp4" />
              </video>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="bg-gradient-to-b from-black to-crystal">
      <div class="max-w-7xl mx-auto px-4">
        <h2 class="text-3xl sm:text-4xl md:text-6xl font-exo text-center mb-12 text-gold liquid-gold">What People Are Saying</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div class="reveal prism-card p-8 rounded-2xl">
            <p class="text-lightgold font-space mb-4">"Klicktape will be a breath of fresh air in the social media landscape—soon, a platform that finally respects my privacy!"</p>
            <p class="font-bold text-gold">- Maya, Social Media User</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl">
            <p class="text-lightgold font-space mb-4">"I can’t wait to love the ad-free experience—it’ll be so refreshing to use a platform that won’t track my every move!"</p>
            <p class="font-bold text-gold">- Raj, Tech Enthusiast</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl">
            <p class="text-lightgold font-space mb-4">"The anonymous rooms feature is amazing. It’s great to connect with people without worrying about privacy."</p>
            <p class="font-bold text-gold">- Priya, Content Creator</p>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="relative">
      <div class="max-w-7xl mx-auto px-4">
        <h2 class="text-3xl sm:text-4xl md:text-6xl font-exo text-center mb-12 text-gold pulse-prism">Frequently Asked Questions</h2>
        <div class="space-y-12">
          <div class="reveal prism-card p-8 rounded-2xl">
            <h3 class="text-lg sm:text-xl font-exo mb-4 text-gold">What makes Klicktape different?</h3>
            <p class="text-lightgold font-space">Klicktape prioritizes privacy and transparency—no tracking, no ads, just authentic connections.</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl">
            <h3 class="text-lg sm:text-xl font-exo mb-4 text-gold">Is Klicktape free to use?</h3>
            <p class="text-lightgold font-space">Yes, enjoy basic features for free, with an optional premium ad-free upgrade.</p>
          </div>
          <div class="reveal prism-card p-8 rounded-2xl">
            <h3 class="text-lg sm:text-xl font-exo mb-4 text-gold">How does Klicktape ensure privacy?</h3>
            <p class="text-lightgold font-space">End-to-end encryption and strict data policies keep your information secure.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-b from-black to-crystal py-16 border-t border-gold/30 relative overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-gold/20 via-transparent to-prism/20"></div>
        <div class="absolute top-10 left-10 w-32 h-32 border border-gold/30 rotate-45 rounded-lg"></div>
        <div class="absolute bottom-10 right-10 w-24 h-24 border border-prism/30 rotate-12 rounded-lg"></div>
        <div class="absolute top-1/2 left-1/4 w-16 h-16 border border-gold/20 rotate-45 rounded-full"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 relative z-10">
        <!-- Main Footer Content -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">

          <!-- Brand Section -->
          <div class="lg:col-span-1">
            <div class="mb-6">
              <a href="index.html" class="text-3xl font-exo text-gold hover:text-prism transition-all duration-300 hover:shadow-gold">
                Klicktape
              </a>
              <div class="w-16 h-1 bg-gradient-to-r from-gold to-prism mt-2 rounded-full"></div>
            </div>
            <p class="text-lightgold/80 font-space text-sm leading-relaxed mb-4">
              Privacy-first social media platform empowering users with full data control and secure connections.
            </p>
            <div class="flex space-x-4">
              <!-- LinkedIn -->
              <a href="https://www.linkedin.com/company/*********/" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <!-- X (Twitter) -->
              <a href="https://x.com/klicktape" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </a>
              <!-- Instagram -->
              <a href="https://www.instagram.com/klicktape/" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <!-- Facebook -->
              <a href="https://www.facebook.com/profile.php?id=61568703147404" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-lg font-exo text-gold mb-4 relative">
              Quick Links
              <div class="w-8 h-0.5 bg-prism absolute -bottom-1 left-0"></div>
            </h3>
            <ul class="space-y-3 font-space text-sm">
              <li><a href="index.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Home</a></li>
              <li><a href="#features" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Features</a></li>
              <li><a href="#about" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">About Us</a></li>
              <li><a href="#faq" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">FAQ</a></li>
              <li><a href="form/form.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Join Waitlist</a></li>
              <li><a href="#" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Download App</a></li>
            </ul>
          </div>

          <!-- Legal -->
          <div>
            <h3 class="text-lg font-exo text-gold mb-4 relative">
              Legal
              <div class="w-8 h-0.5 bg-prism absolute -bottom-1 left-0"></div>
            </h3>
            <ul class="space-y-3 font-space text-sm">
              <li><a href="privacy-policy.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Privacy Policy</a></li>
              <li><a href="terms-conditions.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Terms & Conditions</a></li>
              <li><a href="child-safety-policy.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Child Safety Policy</a></li>
            </ul>
          </div>

          <!-- Contact & Support -->
          <div>
            <h3 class="text-lg font-exo text-gold mb-4 relative">
              Contact & Support
              <div class="w-8 h-0.5 bg-prism absolute -bottom-1 left-0"></div>
            </h3>
            <ul class="space-y-3 font-space text-sm">
              <li>
                <a href="mailto:<EMAIL>" class="text-lightgold/80 hover:text-gold transition-colors duration-300 flex items-center group">
                  <svg class="w-4 h-4 mr-2 group-hover:text-prism transition-colors" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                  </svg>
                  <EMAIL>
                </a>
              </li>
              <li>
                <a href="tel:+919678011096" class="text-lightgold/80 hover:text-gold transition-colors duration-300 flex items-center group">
                  <svg class="w-4 h-4 mr-2 group-hover:text-prism transition-colors" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  +91 9678011096
                </a>
              </li>
              <li class="text-lightgold/80 flex items-start">
                <svg class="w-4 h-4 mr-2 mt-0.5 text-prism" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                </svg>
                <span class="text-sm">Tarunodoy Road<br>Silchar, Assam 788123<br>India</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom Section -->
        <div class="border-t border-gold/20 pt-8">
          <div class="text-center">
            <p class="text-lightgold/60 font-space text-sm">
              © 2025 Klicktape Pvt Ltd. All rights reserved.
            </p>
            <p class="text-lightgold/40 font-space text-xs mt-1">
              Empowering privacy-first social connections worldwide
            </p>
          </div>
        </div>
      </div>

      <!-- Floating Elements -->
      <div class="absolute top-20 right-20 w-6 h-6 border border-gold/30 rotate-45 animate-pulse hidden lg:block"></div>
      <div class="absolute bottom-32 left-32 w-4 h-4 bg-prism/30 rounded-full animate-bounce hidden lg:block"></div>
    </footer>

    <!-- Scripts -->
    <script>
      // 3D Prism Lattice
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer({ alpha: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      document.getElementById('lattice').appendChild(renderer.domElement);

      const geometry = new THREE.IcosahedronGeometry(10, 1); // Smaller for mobile
      const material = new THREE.MeshBasicMaterial({ color: 0xFFD700, wireframe: true, transparent: true, opacity: 0.3 });
      const lattice = new THREE.Mesh(geometry, material);
      scene.add(lattice);

      camera.position.z = 20;

      let mouseX = 0, mouseY = 0;
      document.addEventListener('mousemove', (e) => {
        mouseX = (e.clientX / window.innerWidth - 0.5) * 0.03;
        mouseY = (e.clientY / window.innerHeight - 0.5) * 0.03;
      });

      function animate() {
        requestAnimationFrame(animate);
        lattice.rotation.x += 0.001 + mouseY;
        lattice.rotation.y += 0.001 + mouseX;
        renderer.render(scene, camera);
      }
      animate();

      window.addEventListener('resize', () => {
        renderer.setSize(window.innerWidth, window.innerHeight);
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
      });

      // Custom Cursor
      const cursor = document.querySelector('.cursor');
      document.addEventListener('mousemove', (e) => {
        cursor.style.left = `${e.clientX}px`;
        cursor.style.top = `${e.clientY}px`;
      });
      document.querySelectorAll('a, button, .prism-card').forEach(el => {
        el.addEventListener('mouseenter', () => cursor.classList.add('hover'));
        el.addEventListener('mouseleave', () => cursor.classList.remove('hover'));
      });

      // Scroll Reveal
      const reveals = document.querySelectorAll('.reveal');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) entry.target.classList.add('active');
        });
      }, { threshold: 0.2 });
      reveals.forEach(reveal => observer.observe(reveal));

      // Mobile Menu Toggle
      document.addEventListener('DOMContentLoaded', () => {
        const hamburger = document.querySelector('.hamburger');
        const mobileMenu = document.querySelector('.mobile-menu');
        const closeMenu = document.querySelector('.close-menu');

        // Open menu
        hamburger.addEventListener('click', () => {
          mobileMenu.classList.add('active');
        });

        // Close menu
        closeMenu.addEventListener('click', () => {
          mobileMenu.classList.remove('active');
        });

        // Close menu when clicking a link (optional enhancement)
        mobileMenu.querySelectorAll('a').forEach(link => {
          link.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
          });
        });
      });
    </script>

    <!-- Firebase -->
    <script type="module">
      import { initializeApp } from "https://www.gstatic.com/firebasejs/11.2.0/firebase-app.js";
      const firebaseConfig = {
        apiKey: "AIzaSyBArk7fEX0Jgi26Phtp1t96XY-FxEtkN7M",
        authDomain: "klicktape-d087a.firebaseapp.com",
        projectId: "klicktape-d087a",
        storageBucket: "klicktape-d087a.firebasestorage.app",
        messagingSenderId: "537131358526",
        appId: "1:537131358526:web:22ba55670809a5b107746f"
      };
      const app = initializeApp(firebaseConfig);
    </script>
  </body>
</html>